# Sección de Sectores - Documentación

## 📋 Descripción General

La **SectorsSection** es un componente interactivo que muestra los sectores industriales en los que trabaja Bambú Tech Services. Presenta un carrusel automático con navegación manual que muestra 3 sectores a la vez del lado izquierdo y un proyecto destacado del lado derecho.

## 🎯 Características Principales

### ✨ **Carrusel Automático**
- **Rotación automática** cada 4 segundos
- **Pausa inteligente** cuando el usuario interactúa manualmente
- **Reanudación automática** después de 10 segundos de inactividad

### 🎨 **Diseño Visual**
- **Colores dinámicos** por sector usando CSS custom properties
- **Efectos de hover** y transiciones suaves
- **Patrones animados** de fondo con partículas
- **Responsive design** completo

### 🖱️ **Interactividad**
- **Navegación manual** por clicks en sectores
- **Puntos de navegación** en la parte inferior
- **Estados visuales** claros (activo, hover, etc.)

## 🏗️ Estructura del Componente

### **Props Interface**
```typescript
interface SectorsSectionProps {
  language?: 'es' | 'en';  // Idioma (español por defecto)
}
```

### **Datos de Sectores**
Cada sector incluye:
- **ID único** para identificación
- **Nombre** del sector
- **Proyecto asociado** con:
  - Nombre del proyecto
  - Empresa cliente
  - Descripción detallada
  - Color temático único

## 🎨 Sistema de Colores

Cada sector tiene un color único que se aplica dinámicamente:

| Sector | Color | Hex Code |
|--------|-------|----------|
| Retail | Naranja | `#ff6b35` |
| Banking | Azul | `#2563eb` |
| Fintech | Púrpura | `#7c3aed` |
| E-commerce | Rojo | `#dc2626` |
| Car Sales | Naranja Oscuro | `#ea580c` |
| Gas Stations | Amarillo | `#eab308` |
| Ticketing | Rosa | `#ec4899` |
| Insurance | Cian | `#06b6d4` |
| Logistics | Verde | `#059669` |
| Government | Índigo | `#4338ca` |
| Energy | Verde Claro | `#16a34a` |
| Real Estate | Rosa Oscuro | `#be185d` |
| EdTech | Azul Claro | `#0891b2` |
| CyberSecurity | Rojo Oscuro | `#b91c1c` |
| Transportation | Marrón | `#7c2d12` |
| Construction | Amarillo Oscuro | `#a16207` |

## 🔧 Funcionalidades Técnicas

### **Auto-play System**
```typescript
// Configuración del auto-play
const AUTO_PLAY_INTERVAL = 4000; // 4 segundos
const PAUSE_DURATION = 10000;    // 10 segundos de pausa
```

### **Estados del Componente**
- `currentSectorIndex`: Índice del sector actualmente activo
- `isAutoPlaying`: Estado del auto-play (activo/pausado)
- `visibleSectors`: Array de 3 sectores visibles [prev, current, next]

### **Intersection Observer**
- Detecta cuando la sección está visible
- Puede implementar lógica adicional basada en scroll

## 🎭 Animaciones y Efectos

### **Transiciones de Sectores**
- **Posición**: `prev`, `current`, `next`
- **Opacidad**: Gradual según la posición
- **Escala**: Ligera reducción para sectores no activos
- **Transform**: Desplazamiento horizontal suave

### **Efectos de Proyecto**
- **Patrones animados** de fondo con CSS keyframes
- **Hover effects** con elevación y glow
- **Colores dinámicos** usando CSS custom properties

## 📱 Responsive Design

### **Breakpoints**
- **Desktop**: Grid de 2 columnas (lista + proyecto)
- **Tablet** (≤768px): Grid de 1 columna, altura reducida
- **Mobile** (≤480px): Diseño compacto, padding ajustado

### **Adaptaciones**
- **Tamaños de fuente** escalables
- **Espaciado** proporcional
- **Altura de tarjetas** adaptativa

## 🚀 Uso e Integración

### **Importación**
```typescript
import SectorsSection from './components/SectorsSection';
```

### **Implementación**
```jsx
<SectorsSection language="es" />
```

### **En App.tsx**
```typescript
function App() {
  const [language, setLanguage] = useState<'es' | 'en'>('es');
  
  return (
    <div className="app">
      {/* ... otros componentes ... */}
      <SectorsSection language={language} />
    </div>
  );
}
```

## 🎨 Personalización

### **Agregar Nuevos Sectores**
1. Añadir nuevo objeto al array `sectors`
2. Definir color único
3. Crear proyecto asociado
4. Actualizar textos multiidioma si es necesario

### **Modificar Colores**
Los colores se definen en el array de sectores y se aplican dinámicamente usando CSS custom properties:
```typescript
project: {
  // ... otros campos
  color: '#nuevo-color'
}
```

### **Ajustar Timing**
Modificar las constantes de tiempo en los useEffect:
```typescript
// Intervalo de auto-play
setInterval(() => { /* ... */ }, 4000);

// Duración de pausa
setTimeout(() => setIsAutoPlaying(true), 10000);
```

## 🔍 Debugging y Mantenimiento

### **Estados a Monitorear**
- `currentSectorIndex`: Debe estar entre 0 y sectors.length-1
- `visibleSectors`: Siempre debe tener 3 elementos
- `isAutoPlaying`: Controla el comportamiento automático

### **Posibles Mejoras**
- [ ] Lazy loading de imágenes de proyectos
- [ ] Animaciones más complejas con Framer Motion
- [ ] Integración con CMS para contenido dinámico
- [ ] Analytics de interacción de usuarios
- [ ] Preload de sectores siguientes

## 📊 Performance

### **Optimizaciones Implementadas**
- **CSS custom properties** para colores dinámicos
- **Intersection Observer** para detección de visibilidad
- **Cleanup de timers** en useEffect
- **Transiciones CSS** en lugar de JavaScript

### **Métricas Esperadas**
- **Tiempo de carga**: < 100ms
- **Smooth animations**: 60fps
- **Memory usage**: Mínimo (cleanup automático)

---

*Documentación actualizada - Enero 2025*
