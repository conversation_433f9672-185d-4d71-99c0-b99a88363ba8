<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Generate Project Images</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            background: #000;
            color: white;
        }
        
        .image-generator {
            width: 600px;
            height: 400px;
            margin: 20px 0;
            position: relative;
            border-radius: 15px;
            overflow: hidden;
            background: linear-gradient(135deg, #000 0%, #1a1a1a 50%, #000 100%);
            border: 2px solid #00ff00;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
        }
        
        .project-content {
            z-index: 2;
            position: relative;
        }
        
        .project-title {
            font-size: 2rem;
            font-weight: bold;
            color: #00ff00;
            margin-bottom: 10px;
            text-shadow: 0 0 10px rgba(0, 255, 0, 0.5);
        }
        
        .project-sector {
            font-size: 1.2rem;
            color: white;
            opacity: 0.8;
        }
        
        .bg-pattern {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0.1;
            background-image: 
                radial-gradient(circle at 25% 25%, #00ff00 2px, transparent 2px),
                radial-gradient(circle at 75% 75%, #00ff00 1px, transparent 1px);
            background-size: 50px 50px;
        }
        
        .instructions {
            background: #1a1a1a;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            border: 1px solid #333;
        }
        
        .download-btn {
            background: #00ff00;
            color: black;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            margin: 10px;
        }
    </style>
</head>
<body>
    <div class="instructions">
        <h2>Generador de Imágenes de Proyectos</h2>
        <p>Estas son imágenes de placeholder para los proyectos de cada sector. Puedes hacer screenshot de cada una y guardarlas en la carpeta <code>public/projects/</code> con los nombres correspondientes.</p>
    </div>

    <div class="image-generator" id="retail-pos">
        <div class="bg-pattern"></div>
        <div class="project-content">
            <div class="project-title">Sistema POS Inteligente</div>
            <div class="project-sector">Retail</div>
        </div>
    </div>
    <button class="download-btn" onclick="downloadImage('retail-pos', 'retail-pos.jpg')">Descargar Retail</button>

    <div class="image-generator" id="banking-app">
        <div class="bg-pattern"></div>
        <div class="project-content">
            <div class="project-title">App Bancaria Digital</div>
            <div class="project-sector">Banking</div>
        </div>
    </div>
    <button class="download-btn" onclick="downloadImage('banking-app', 'banking-app.jpg')">Descargar Banking</button>

    <div class="image-generator" id="payment-gateway">
        <div class="bg-pattern"></div>
        <div class="project-content">
            <div class="project-title">Gateway de Pagos</div>
            <div class="project-sector">Fintech</div>
        </div>
    </div>
    <button class="download-btn" onclick="downloadImage('payment-gateway', 'payment-gateway.jpg')">Descargar Fintech</button>

    <div class="image-generator" id="marketplace">
        <div class="bg-pattern"></div>
        <div class="project-content">
            <div class="project-title">Marketplace B2B</div>
            <div class="project-sector">E-commerce</div>
        </div>
    </div>
    <button class="download-btn" onclick="downloadImage('marketplace', 'marketplace.jpg')">Descargar E-commerce</button>

    <script src="https://html2canvas.hertzen.com/dist/html2canvas.min.js"></script>
    <script>
        function downloadImage(elementId, filename) {
            const element = document.getElementById(elementId);
            html2canvas(element, {
                backgroundColor: null,
                scale: 2
            }).then(canvas => {
                const link = document.createElement('a');
                link.download = filename;
                link.href = canvas.toDataURL('image/jpeg', 0.9);
                link.click();
            });
        }
        
        // Auto-generate all images
        function generateAllImages() {
            const projects = [
                'retail-pos', 'banking-app', 'payment-gateway', 'marketplace'
            ];
            
            projects.forEach((project, index) => {
                setTimeout(() => {
                    downloadImage(project, `${project}.jpg`);
                }, index * 1000);
            });
        }
        
        // Add button to generate all
        const generateAllBtn = document.createElement('button');
        generateAllBtn.textContent = 'Generar Todas las Imágenes';
        generateAllBtn.className = 'download-btn';
        generateAllBtn.style.background = '#ff6600';
        generateAllBtn.onclick = generateAllImages;
        document.body.appendChild(generateAllBtn);
    </script>
</body>
</html>
