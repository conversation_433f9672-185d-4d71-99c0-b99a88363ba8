import { useState } from 'react'
import Navbar from './components/Navbar'
import HeroSection from './components/HeroSection'
import StatsSection from './components/StatsSection'
import ServicesSection from './components/ServicesSection'
import SectorsSection from './components/SectorsSection'
import './App.css'

function App() {
  const [language, setLanguage] = useState<'es' | 'en'>('es');

  return (
    <div className="app">
      <Navbar language={language} onLanguageChange={setLanguage} />
      <HeroSection language={language} />
      <StatsSection />
      <ServicesSection />
      <SectorsSection language={language} />
    </div>
  )
}

export default App
