import React, { useEffect, useRef } from 'react';
import { Code, Workflow, Briefcase, Users, Server, Leaf, Play } from 'lucide-react';
import './ServicesSection.css';
import './PixelCanvas.js';

interface ServiceData {
  id: string;
  title: string;
  description: string;
  cta: string;
  icon: string;
  size: 'large' | 'medium' | 'small';
}

const ServicesSection: React.FC = () => {
  const sectionRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Add pixel-canvas elements to service cards after component mounts
    const serviceCards = document.querySelectorAll('.service-card');
    serviceCards.forEach((card) => {
      if (!card.querySelector('pixel-canvas')) {
        const pixelCanvas = document.createElement('pixel-canvas');
        pixelCanvas.setAttribute('data-colors', '#00ff00,#ffffff,#00ff00,#00ff00');
        pixelCanvas.setAttribute('data-gap', '9');
        pixelCanvas.setAttribute('data-speed', '30');
        card.insertBefore(pixelCanvas, card.firstChild);
      }
    });
  }, []);

  const getIcon = (iconName: string) => {
    const iconProps = { size: 36, className: "service-icon-svg" };
    switch (iconName) {
      case 'Code': return <Code {...iconProps} />;
      case 'Workflow': return <Workflow {...iconProps} />;
      case 'Briefcase': return <Briefcase {...iconProps} />;
      case 'Users': return <Users {...iconProps} />;
      case 'Server': return <Server {...iconProps} />;
      case 'Leaf': return <Leaf {...iconProps} />;
      case 'IsoLogo': return (
        <svg width="36" height="28" viewBox="0 0 792 612" className="service-icon-svg">
          <polygon
            fill="currentColor"

            points="510.1 77 337.7 77 60.6 534.2 233.1 534.2 510.1 77"
          />
          <polygon
            fill="currentColor"

            points="731.4 392.6 645.1 535 472.7 535 558.9 392.6 497.6 250.3 670 250.3 731.4 392.6"
          />
        </svg>
      );
      default: return <Code {...iconProps} />;
    }
  };

  const services: ServiceData[] = [
    {
      id: 'software-dev',
      title: 'Software Development',
      description: 'Creamos software a la medida que se adapta a tu negocio. Desde aplicaciones web y móviles hasta plataformas complejas, desarrollamos soluciones seguras, escalables y orientadas a resultados.',
      cta: 'Impulsa tu negocio con tecnología hecha para ti.',
      icon: 'Code',
      size: 'large'
    },
    {
      id: 'bambu-care',
      title: 'Bambú Care',
      description: 'Nuestro servicio integral de soporte y mantenimiento. Con Bambú Care aseguramos estabilidad, evolución continua y atención experta para que tu plataforma digital nunca se detenga.',
      cta: 'Soporte que impulsa el valor de tu negocio día a día.',
      icon: 'IsoLogo',
      size: 'small'
    },

    {
      id: 'consulting',
      title: 'Consulting',
      description: 'Toma decisiones estratégicas con el respaldo de nuestra experiencia. Evaluamos tu situación tecnológica y diseñamos planes de acción que optimizan procesos y maximizan el ROI de tus proyectos digitales.',
      cta: 'Asesoría experta para crecer con confianza.',
      icon: 'Briefcase',
      size: 'medium'
    },
    {
      id: 'staff-augmentation',
      title: 'Staff Augmentation',
      description: 'Refuerza tu equipo con talento especializado en tecnología sin aumentar tu nómina. Te brindamos perfiles expertos listos para integrarse y acelerar tus proyectos.',
      cta: 'El equipo que necesitas, justo cuando lo necesitas.',
      icon: 'Users',
      size: 'small'
    },
    {
      id: 'rpa-automation',
      title: 'RPA & Automation',
      description: 'Automatiza procesos repetitivos y reduce tiempos operativos. Implementamos robots de software y flujos inteligentes que aumentan la eficiencia y eliminan errores.',
      cta: 'Más productividad, menos tareas manuales.',
      icon: 'Workflow',
      size: 'medium'
    },
    {
      id: 'infrastructure',
      title: 'Infrastructure & Hardware',
      description: 'Diseñamos y gestionamos tu infraestructura tecnológica para garantizar estabilidad, seguridad y crecimiento escalable. Desde servidores hasta soluciones cloud, tu operación nunca se detiene.',
      cta: 'Infraestructura sólida para un negocio sólido.',
      icon: 'Server',
      size: 'large'
    }


  ];

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add('visible');
          }
        });
      },
      {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
      }
    );

    const serviceCards = document.querySelectorAll('.service-card');
    serviceCards.forEach((card) => observer.observe(card));

    return () => {
      serviceCards.forEach((card) => observer.unobserve(card));
    };
  }, []);

  return (
    <section className="services-section" ref={sectionRef}>
      <div className="services-container">
        <div className="services-header">
          <h2 className="services-title">lo que podemos hacer por ti</h2>
          <p className="services-subtitle">
            Descubre nuestras soluciones digitales diseñadas para aumentar la productividad,
            reducir costos y hacer crecer tu empresa.
          </p>
        </div>

        <div className="services-grid">
          <div className="service-card item-0">
            <div className="service-icon">{getIcon(services[0].icon)}</div>
            <h3 className="service-title">{services[0].title}</h3>
            <p className="service-description">{services[0].description}</p>
            <div className="service-video-cta">
              <Play size={16} className="video-icon" />
              <span className="video-text">ver video</span>
            </div>
            <div className="card-overlay"></div>
          </div>

          <div className="service-card item-1">
            <div className="service-icon">{getIcon(services[1].icon)}</div>
            <h3 className="service-title">{services[1].title}</h3>
            <p className="service-description">{services[1].description}</p>
            <div className="service-video-cta">
              <Play size={16} className="video-icon" />
              <span className="video-text">ver video</span>
            </div>
            <div className="card-overlay"></div>
          </div>

          <div className="service-card item-2">
            <div className="service-icon">{getIcon(services[2].icon)}</div>
            <h3 className="service-title">{services[2].title}</h3>
            <p className="service-description">{services[2].description}</p>
            <div className="service-video-cta">
              <Play size={16} className="video-icon" />
              <span className="video-text">ver video</span>
            </div>
            <div className="card-overlay"></div>
          </div>

          <div className="service-card item-3">
            <div className="service-icon">{getIcon(services[3].icon)}</div>
            <h3 className="service-title">{services[3].title}</h3>
            <p className="service-description">{services[3].description}</p>
            <div className="service-video-cta">
              <Play size={16} className="video-icon" />
              <span className="video-text">ver video</span>
            </div>
            <div className="card-overlay"></div>
          </div>

          <div className="service-card item-4">
            <div className="service-icon">{getIcon(services[4].icon)}</div>
            <h3 className="service-title">{services[4].title}</h3>
            <p className="service-description">{services[4].description}</p>
            <div className="service-video-cta">
              <Play size={16} className="video-icon" />
              <span className="video-text">ver video</span>
            </div>
            <div className="card-overlay"></div>
          </div>

          <div className="service-card item-5">
            <div className="service-icon">{getIcon(services[5].icon)}</div>
            <h3 className="service-title">{services[5].title}</h3>
            <p className="service-description">{services[5].description}</p>
            <div className="service-video-cta">
              <Play size={16} className="video-icon" />
              <span className="video-text">ver video</span>
            </div>
            <div className="card-overlay"></div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ServicesSection;