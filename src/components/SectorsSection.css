/* ===== SECTORS SECTION ===== */
.sectors-section {
  position: relative;
  min-height: 100vh;
  padding: 4rem 0;
  background: #000;
  overflow: hidden;
}

.sectors-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* ===== HEADER ===== */
.sectors-header {
  text-align: center;
  margin-bottom: 4rem;
}

.sectors-title {
  font-family: 'Sansation', sans-serif;
  font-size: 3.5rem;
  font-weight: 700;
  background: linear-gradient(180deg, #ffffff 0%, #e5e5e5 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  letter-spacing: -0.02em;
  line-height: 1.2;
  margin-bottom: 1rem;
  text-transform: lowercase;
}

.sectors-subtitle {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.7);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* ===== MAIN CONTENT ===== */
.sectors-content {
  display: grid;
  grid-template-columns: 1fr 1.5fr;
  gap: 4rem;
  align-items: center;
  min-height: 600px;
}

/* ===== LEFT SIDE - SECTORS LIST ===== */
.sectors-list {
  position: relative;
  padding: 2rem 0;
}

.sector-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem 2rem;
  margin-bottom: 1rem;
  border-radius: 15px;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  opacity: 0.6;
  transform: translateX(-20px);
}

.sector-item.current {
  opacity: 1;
  transform: translateX(0);
}

.sector-item.prev {
  opacity: 0.4;
  transform: translateX(-10px) scale(0.95);
}

.sector-item.next {
  opacity: 0.4;
  transform: translateX(-10px) scale(0.95);
}

.sector-item.active {
  background: rgba(0, 255, 0, 0.1);
  border-color: #00ff00;
  box-shadow: 0 0 20px rgba(0, 255, 0, 0.2);
  opacity: 1;
  transform: translateX(0) scale(1.02);
}

.sector-item:hover:not(.active) {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.15);
  transform: translateX(5px);
  opacity: 0.8;
}

.sector-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: white;
  transition: color 0.3s ease;
}

.sector-item.active .sector-name {
  color: #00ff00;
}

.sector-arrow {
  color: #00ff00;
  transition: transform 0.3s ease;
}

.sector-item.active .sector-arrow {
  transform: translateX(5px);
}

/* ===== NAVIGATION DOTS ===== */
.sectors-navigation {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 2rem;
  padding: 1rem;
}

.nav-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  border: none;
  background: rgba(255, 255, 255, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
}

.nav-dot.active {
  background: #00ff00;
  box-shadow: 0 0 10px rgba(0, 255, 0, 0.5);
  transform: scale(1.2);
}

.nav-dot:hover:not(.active) {
  background: rgba(255, 255, 255, 0.5);
  transform: scale(1.1);
}

/* ===== RIGHT SIDE - PROJECT SHOWCASE ===== */
.project-showcase {
  position: relative;
  height: 600px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.project-card {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 20px;
  overflow: hidden;
  background: linear-gradient(135deg,
    rgba(0, 0, 0, 0.8),
    rgba(26, 26, 26, 0.9)
  );
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.project-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px var(--project-color-alpha, rgba(0, 255, 0, 0.1));
  border-color: var(--project-color, rgba(0, 255, 0, 0.3));
}

.project-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg,
    var(--project-color-alpha, rgba(0, 255, 0, 0.05)) 0%,
    rgba(0, 0, 0, 0.8) 50%,
    rgba(0, 0, 0, 0.9) 100%
  );
  z-index: 1;
}

.project-image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at top right, var(--project-color-alpha, rgba(0, 255, 0, 0.1)) 0%, transparent 50%),
    radial-gradient(circle at 25% 25%, var(--project-color-alpha, rgba(0, 255, 0, 0.05)) 2px, transparent 2px),
    radial-gradient(circle at 75% 75%, var(--project-color-alpha, rgba(0, 255, 0, 0.03)) 1px, transparent 1px);
  background-size: 100% 100%, 50px 50px, 30px 30px;
  animation: patternMove 20s linear infinite;
}

@keyframes patternMove {
  0% { background-position: 0% 0%, 0px 0px, 0px 0px; }
  100% { background-position: 0% 0%, 50px 50px, 30px 30px; }
}

.project-content {
  position: relative;
  z-index: 2;
  padding: 3rem;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.project-header {
  display: flex;
  align-items: flex-start;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.project-logo {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid rgba(255, 255, 255, 0.2);
  flex-shrink: 0;
}

.project-logo img {
  width: 40px;
  height: 40px;
  object-fit: contain;
  filter: brightness(0) invert(1);
}

.project-logo-placeholder {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: bold;
  color: var(--project-color, #00ff00);
  background: var(--project-color-alpha, rgba(0, 255, 0, 0.1));
  border-radius: 8px;
  border: 1px solid var(--project-color, #00ff00);
}

.project-info {
  flex: 1;
}

.project-name {
  font-size: 2rem;
  font-weight: 700;
  color: white;
  margin-bottom: 0.5rem;
  line-height: 1.2;
}

.project-company {
  font-size: 1rem;
  color: var(--project-color, #00ff00);
  font-weight: 500;
}

.project-description {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  margin-bottom: 2rem;
  flex: 1;
}

.project-cta {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: var(--project-color-alpha, rgba(0, 255, 0, 0.1));
  border: 1px solid var(--project-color, #00ff00);
  color: var(--project-color, #00ff00);
  padding: 1rem 1.5rem;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  align-self: flex-start;
}

.project-cta:hover {
  background: var(--project-color, #00ff00);
  color: black;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px var(--project-color-alpha, rgba(0, 255, 0, 0.3));
}

.project-cta:active {
  transform: translateY(0);
}

/* ===== RESPONSIVE DESIGN ===== */

/* Tablet */
@media (max-width: 768px) {
  .sectors-section {
    padding: 3rem 0;
  }

  .sectors-container {
    padding: 0 1.5rem;
  }

  .sectors-title {
    font-size: 2.8rem;
  }

  .sectors-subtitle {
    font-size: 1.1rem;
  }

  .sectors-content {
    grid-template-columns: 1fr;
    gap: 3rem;
  }

  .project-showcase {
    height: 500px;
  }

  .project-content {
    padding: 2rem;
  }

  .project-name {
    font-size: 1.6rem;
  }

  .sector-item {
    padding: 1.25rem 1.5rem;
  }
}

/* Mobile */
@media (max-width: 480px) {
  .sectors-section {
    padding: 2rem 0;
  }

  .sectors-container {
    padding: 0 1rem;
  }

  .sectors-title {
    font-size: 2.2rem;
  }

  .sectors-subtitle {
    font-size: 1rem;
  }

  .sectors-header {
    margin-bottom: 3rem;
  }

  .sectors-content {
    gap: 2rem;
  }

  .project-showcase {
    height: 400px;
  }

  .project-content {
    padding: 1.5rem;
  }

  .project-header {
    gap: 1rem;
    margin-bottom: 1.5rem;
  }

  .project-logo {
    width: 50px;
    height: 50px;
  }

  .project-logo img {
    width: 30px;
    height: 30px;
  }

  .project-name {
    font-size: 1.4rem;
  }

  .project-description {
    font-size: 1rem;
    margin-bottom: 1.5rem;
  }

  .sector-item {
    padding: 1rem 1.25rem;
    margin-bottom: 0.75rem;
  }

  .sector-name {
    font-size: 1rem;
  }
}
