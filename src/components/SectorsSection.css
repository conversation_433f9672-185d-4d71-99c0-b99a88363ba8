/* ===== SECTORS SECTION ===== */
.sectors-section {
  position: relative;
  min-height: 100vh;
  padding: 4rem 0;
  background: #000;
  overflow: hidden;
}

.sectors-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* ===== HEADER ===== */
.sectors-header {
  text-align: center;
  margin-bottom: 4rem;
}

.sectors-title {
  font-family: 'Sansation', sans-serif;
  font-size: 3.5rem;
  font-weight: 700;
  background: linear-gradient(180deg, #ffffff 0%, #e5e5e5 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  letter-spacing: -0.02em;
  line-height: 1.2;
  margin-bottom: 1rem;
  text-transform: lowercase;
}

.sectors-subtitle {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.7);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* ===== MAIN CONTENT ===== */
.sectors-content {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 5rem;
  align-items: center;
  min-height: 700px;
}

/* ===== LEFT SIDE - SECTORS LIST ===== */
.sectors-list {
  position: relative;
  padding: 3rem 0;
  height: 500px;
  overflow: hidden;
}

/* Selector fijo en el centro */
.sector-selector {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 80px;
  transform: translateY(-50%);
  background: rgba(0, 255, 0, 0.1);
  border: 2px solid #00ff00;
  border-radius: 20px;
  box-shadow:
    0 0 30px rgba(0, 255, 0, 0.2),
    inset 0 0 20px rgba(0, 255, 0, 0.05);
  z-index: 5;
  pointer-events: none;
  margin: 0 0 1.5rem 0;
}

/* Contenedor de la lista que se mueve */
.sectors-carousel {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  transition: transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  padding-top: 400px;
  padding-bottom: 400px;
}

.sector-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.8rem 2.5rem;
  margin-bottom: 1.5rem;
  height: 80px;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  position: relative;
  overflow: hidden;
  z-index: 10;
}

/* Estados basados en distancia del centro */
.sector-item {
  opacity: 0.3;
  transform: scale(0.85);
  filter: blur(1px);
}

.sector-item.center {
  opacity: 1;
  transform: scale(1);
  filter: blur(0);
  background: rgba(0, 255, 0, 0.05);
  border-color: rgba(0, 255, 0, 0.3);
}

.sector-item.near-center {
  opacity: 0.7;
  transform: scale(0.95);
  filter: blur(0.5px);
}

.sector-item:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.2);
  opacity: 0.8;
}

.sector-name {
  font-size: 1.2rem;
  font-weight: 600;
  color: white;
  transition: all 0.6s ease;
  position: relative;
}

.sector-item.center .sector-name {
  color: #00ff00;
  text-shadow: 0 0 10px rgba(0, 255, 0, 0.3);
  font-weight: 700;
}

.sector-arrow {
  color: #00ff00;
  transition: all 0.6s ease;
  opacity: 0;
  transform: translateX(-10px);
}

.sector-item.center .sector-arrow {
  opacity: 1;
  transform: translateX(0);
}

/* Efecto de brillo en el sector central */
.sector-item.center::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(0, 255, 0, 0.1),
    transparent
  );
  animation: shimmer 3s infinite;
}

@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* ===== NAVIGATION DOTS ===== */
.sectors-navigation {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 2rem;
  padding: 1rem;
}

.nav-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  border: none;
  background: rgba(255, 255, 255, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
}

.nav-dot.active {
  background: #00ff00;
  box-shadow: 0 0 10px rgba(0, 255, 0, 0.5);
  transform: scale(1.2);
}

.nav-dot:hover:not(.active) {
  background: rgba(255, 255, 255, 0.5);
  transform: scale(1.1);
}

/* ===== RIGHT SIDE - PROJECT SHOWCASE ===== */
.project-showcase {
  position: relative;
  height: 700px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.project-showcase:hover {
  transform: scale(1.02);
}

/* Indicador de pausa */
.project-showcase::after {
  content: '';
  position: absolute;
  top: 20px;
  right: 20px;
  width: 12px;
  height: 12px;
  background: #ff6b35;
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 100;
}

.project-showcase:hover::after {
  opacity: 1;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.2); opacity: 0.7; }
}

.project-card {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 25px;
  overflow: hidden;
  background: linear-gradient(135deg,
    rgba(0, 0, 0, 0.8),
    rgba(26, 26, 26, 0.9)
  );
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(25px);
  -webkit-backdrop-filter: blur(25px);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  animation: projectFadeIn 0.8s ease-out;
}

.project-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow:
    0 25px 50px var(--project-color-alpha, rgba(0, 255, 0, 0.15)),
    0 0 40px var(--project-color-alpha, rgba(0, 255, 0, 0.1));
  border-color: var(--project-color, rgba(0, 255, 0, 0.4));
}

@keyframes projectFadeIn {
  0% {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.project-card.transitioning {
  animation: projectTransition 0.6s ease-out;
}

@keyframes projectTransition {
  0% {
    opacity: 0.7;
    transform: translateX(20px) scale(0.98);
  }
  50% {
    opacity: 0.9;
    transform: translateX(-5px) scale(1.01);
  }
  100% {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

.project-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg,
    var(--project-color-alpha, rgba(0, 255, 0, 0.05)) 0%,
    rgba(0, 0, 0, 0.8) 50%,
    rgba(0, 0, 0, 0.9) 100%
  );
  z-index: 1;
}

.project-image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at top right, var(--project-color-alpha, rgba(0, 255, 0, 0.1)) 0%, transparent 50%),
    radial-gradient(circle at 25% 25%, var(--project-color-alpha, rgba(0, 255, 0, 0.05)) 2px, transparent 2px),
    radial-gradient(circle at 75% 75%, var(--project-color-alpha, rgba(0, 255, 0, 0.03)) 1px, transparent 1px);
  background-size: 100% 100%, 50px 50px, 30px 30px;
  animation: patternMove 20s linear infinite;
}

@keyframes patternMove {
  0% { background-position: 0% 0%, 0px 0px, 0px 0px; }
  100% { background-position: 0% 0%, 50px 50px, 30px 30px; }
}

.project-content {
  position: relative;
  z-index: 2;
  padding: 4rem;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.project-header {
  display: flex;
  align-items: flex-start;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.project-logo {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid rgba(255, 255, 255, 0.2);
  flex-shrink: 0;
}

.project-logo img {
  width: 40px;
  height: 40px;
  object-fit: contain;
  filter: brightness(0) invert(1);
}

.project-logo-placeholder {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: bold;
  color: var(--project-color, #00ff00);
  background: var(--project-color-alpha, rgba(0, 255, 0, 0.1));
  border-radius: 8px;
  border: 1px solid var(--project-color, #00ff00);
}

.project-info {
  flex: 1;
}

.project-name {
  font-size: 2.5rem;
  font-weight: 700;
  color: white;
  margin-bottom: 0.5rem;
  line-height: 1.2;
}

.project-company {
  font-size: 1rem;
  color: var(--project-color, #00ff00);
  font-weight: 500;
}

.project-description {
  font-size: 1.3rem;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.7;
  margin-bottom: 2.5rem;
  flex: 1;
}

.project-cta {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: var(--project-color-alpha, rgba(0, 255, 0, 0.1));
  border: 1px solid var(--project-color, #00ff00);
  color: var(--project-color, #00ff00);
  padding: 1rem 1.5rem;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  align-self: flex-start;
}

.project-cta:hover {
  background: var(--project-color, #00ff00);
  color: black;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px var(--project-color-alpha, rgba(0, 255, 0, 0.3));
}

.project-cta:active {
  transform: translateY(0);
}

/* ===== RESPONSIVE DESIGN ===== */

/* Tablet */
@media (max-width: 768px) {
  .sectors-section {
    padding: 3rem 0;
  }

  .sectors-container {
    padding: 0 1.5rem;
  }

  .sectors-title {
    font-size: 2.8rem;
  }

  .sectors-subtitle {
    font-size: 1.1rem;
  }

  .sectors-content {
    grid-template-columns: 1fr;
    gap: 3rem;
  }

  .sectors-list {
    height: 400px;
  }

  .sector-selector {
    height: 70px;
  }

  .sector-item {
    height: 70px;
    padding: 1.2rem 2rem;
    margin-bottom: 1.2rem;
  }

  .sectors-carousel {
    padding-top: 300px;
    padding-bottom: 300px;
  }

  .project-showcase {
    height: 600px;
  }

  .project-content {
    padding: 3rem;
  }

  .project-name {
    font-size: 2rem;
  }

  .project-description {
    font-size: 1.2rem;
  }
}

/* Mobile */
@media (max-width: 480px) {
  .sectors-section {
    padding: 2rem 0;
  }

  .sectors-container {
    padding: 0 1rem;
  }

  .sectors-title {
    font-size: 2.2rem;
  }

  .sectors-subtitle {
    font-size: 1rem;
  }

  .sectors-header {
    margin-bottom: 3rem;
  }

  .sectors-content {
    gap: 2rem;
    grid-template-columns: 1fr;
  }

  .sectors-list {
    height: 300px;
  }

  .sector-selector {
    height: 60px;
  }

  .sector-item {
    height: 60px;
    padding: 1rem 1.5rem;
    margin-bottom: 1rem;
  }

  .sectors-carousel {
    padding-top: 240px;
    padding-bottom: 240px;
  }

  .project-showcase {
    height: 500px;
  }

  .project-content {
    padding: 2rem;
  }

  .project-header {
    gap: 1rem;
    margin-bottom: 1.5rem;
  }

  .project-logo {
    width: 50px;
    height: 50px;
  }

  .project-logo-placeholder {
    width: 30px;
    height: 30px;
    font-size: 1.2rem;
  }

  .project-name {
    font-size: 1.8rem;
  }

  .project-description {
    font-size: 1.1rem;
    margin-bottom: 2rem;
  }

  .sector-name {
    font-size: 1rem;
  }
}
