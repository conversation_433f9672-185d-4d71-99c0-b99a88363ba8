import React, { useState, useEffect, useRef } from 'react';
import { ChevronRight } from 'lucide-react';
import './SectorsSection.css';

interface Project {
  id: string;
  name: string;
  company: string;
  description: string;
  color: string;
  logo?: string;
}

interface Sector {
  id: string;
  name: string;
  project: Project;
}

interface SectorsSectionProps {
  language?: 'es' | 'en';
}

const SectorsSection: React.FC<SectorsSectionProps> = ({ language = 'es' }) => {
  const [currentSectorIndex, setCurrentSectorIndex] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);
  const [visibleSectors, setVisibleSectors] = useState<number[]>([0, 1, 2]);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const sectionRef = useRef<HTMLElement>(null);
  const autoPlayRef = useRef<NodeJS.Timeout>();

  const sectors: Sector[] = [
    {
      id: 'retail',
      name: 'Retail',
      project: {
        id: 'retail-pos',
        name: 'Sistema POS Inteligente',
        company: 'MegaStore',
        description: 'Plataforma integral de punto de venta con análisis en tiempo real, gestión de inventario y experiencia de cliente personalizada.',
        color: '#ff6b35'
      }
    },
    {
      id: 'banking',
      name: 'Banking & Non Banking Services',
      project: {
        id: 'banking-app',
        name: 'App Bancaria Digital',
        company: 'FinanceBank',
        description: 'Aplicación móvil bancaria con biometría, transferencias instantáneas y gestión financiera personal avanzada.',
        color: '#2563eb'
      }
    },
    {
      id: 'fintech',
      name: 'Fintech',
      project: {
        id: 'payment-gateway',
        name: 'Gateway de Pagos',
        company: 'PayTech',
        description: 'Solución de pagos digitales con múltiples métodos, seguridad avanzada y análisis de transacciones.',
        color: '#7c3aed'
      }
    },
    {
      id: 'ecommerce',
      name: 'E-commerce',
      project: {
        id: 'marketplace',
        name: 'Marketplace B2B',
        company: 'TradePlatform',
        description: 'Plataforma de comercio electrónico B2B con gestión de proveedores, catálogo inteligente y logística integrada.',
        color: '#dc2626'
      }
    },
    {
      id: 'car-sales',
      name: 'Car Sales & Dealerships',
      project: {
        id: 'auto-crm',
        name: 'CRM Automotriz',
        company: 'AutoDealer Pro',
        description: 'Sistema CRM especializado para concesionarios con gestión de leads, inventario y financiamiento.',
        color: '#ea580c'
      }
    },
    {
      id: 'gas-stations',
      name: 'Gas Stations',
      project: {
        id: 'fuel-management',
        name: 'Sistema de Gestión Combustible',
        company: 'FuelTech',
        description: 'Plataforma de control de inventario, ventas y mantenimiento para estaciones de servicio.',
        color: '#eab308'
      }
    },
    {
      id: 'ticketing',
      name: 'Ticketing Systems',
      project: {
        id: 'event-platform',
        name: 'Plataforma de Eventos',
        company: 'EventMaster',
        description: 'Sistema completo de venta de boletos con gestión de eventos, asientos y análisis de audiencia.',
        color: '#ec4899'
      }
    },
    {
      id: 'insurance',
      name: 'Insurance',
      project: {
        id: 'insurance-portal',
        name: 'Portal de Seguros Digital',
        company: 'SecureLife',
        description: 'Plataforma digital para cotización, contratación y gestión de pólizas de seguros.',
        color: '#06b6d4'
      }
    },
    {
      id: 'logistics',
      name: 'Logistics & Supply Chain Management',
      project: {
        id: 'supply-chain',
        name: 'Sistema de Cadena de Suministro',
        company: 'LogiFlow',
        description: 'Solución integral para gestión de inventario, rutas de entrega y trazabilidad de productos.',
        color: '#059669'
      }
    },
    {
      id: 'government',
      name: 'Government',
      project: {
        id: 'citizen-portal',
        name: 'Portal Ciudadano Digital',
        company: 'GovTech',
        description: 'Plataforma gubernamental para trámites digitales, servicios ciudadanos y transparencia.',
        color: '#4338ca'
      }
    },
    {
      id: 'energy',
      name: 'Energy',
      project: {
        id: 'smart-grid',
        name: 'Red Eléctrica Inteligente',
        company: 'PowerGrid',
        description: 'Sistema de monitoreo y control de red eléctrica con análisis predictivo y eficiencia energética.',
        color: '#16a34a'
      }
    },
    {
      id: 'real-estate',
      name: 'Real Estate',
      project: {
        id: 'property-platform',
        name: 'Plataforma Inmobiliaria',
        company: 'RealtyPro',
        description: 'Sistema completo para gestión de propiedades, tours virtuales y transacciones inmobiliarias.',
        color: '#be185d'
      }
    },
    {
      id: 'edtech',
      name: 'EdTech',
      project: {
        id: 'learning-platform',
        name: 'Plataforma de Aprendizaje',
        company: 'EduTech',
        description: 'LMS avanzado con IA para personalización del aprendizaje, evaluaciones y seguimiento de progreso.',
        color: '#0891b2'
      }
    },
    {
      id: 'cybersecurity',
      name: 'CyberSecurity',
      project: {
        id: 'security-platform',
        name: 'Centro de Operaciones de Seguridad',
        company: 'CyberShield',
        description: 'SOC completo con monitoreo 24/7, detección de amenazas y respuesta automatizada a incidentes.',
        color: '#b91c1c'
      }
    },
    {
      id: 'transportation',
      name: 'Transportation',
      project: {
        id: 'fleet-management',
        name: 'Gestión de Flotas',
        company: 'TransportTech',
        description: 'Sistema de rastreo GPS, optimización de rutas y mantenimiento predictivo para flotas vehiculares.',
        color: '#7c2d12'
      }
    },
    {
      id: 'construction',
      name: 'Construction',
      project: {
        id: 'project-management',
        name: 'Gestión de Proyectos de Construcción',
        company: 'BuildTech',
        description: 'Plataforma para planificación, seguimiento y control de proyectos de construcción con BIM integrado.',
        color: '#a16207'
      }
    }
  ];

  const texts = {
    es: {
      title: 'sectores que transformamos',
      subtitle: 'Desarrollamos soluciones tecnológicas especializadas para cada industria',
      viewMore: 'ver más'
    },
    en: {
      title: 'sectors we transform',
      subtitle: 'We develop specialized technology solutions for each industry',
      viewMore: 'view more'
    }
  };

  // Auto-play functionality
  useEffect(() => {
    if (isAutoPlaying) {
      autoPlayRef.current = setInterval(() => {
        setCurrentSectorIndex((prev) => {
          const nextIndex = (prev + 1) % sectors.length;
          updateVisibleSectors(nextIndex);
          return nextIndex;
        });
      }, 4000);
    }

    return () => {
      if (autoPlayRef.current) {
        clearInterval(autoPlayRef.current);
      }
    };
  }, [isAutoPlaying, sectors.length]);

  // Update visible sectors based on current index
  const updateVisibleSectors = (centerIndex: number) => {
    const prevIndex = centerIndex === 0 ? sectors.length - 1 : centerIndex - 1;
    const nextIndex = (centerIndex + 1) % sectors.length;
    setVisibleSectors([prevIndex, centerIndex, nextIndex]);
  };

  // Handle sector selection
  const handleSectorSelect = (index: number) => {
    if (index === currentSectorIndex || isTransitioning) return;

    setIsTransitioning(true);
    setCurrentSectorIndex(index);
    updateVisibleSectors(index);
    setIsAutoPlaying(false);

    // Reset transition state
    setTimeout(() => setIsTransitioning(false), 300);

    // Resume auto-play after 10 seconds
    setTimeout(() => setIsAutoPlaying(true), 10000);
  };

  // Intersection Observer for scroll-based navigation
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            // Section is visible, can implement scroll-based logic here
          }
        });
      },
      { threshold: 0.5 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, []);

  const currentProject = sectors[currentSectorIndex]?.project;

  return (
    <section className="sectors-section" ref={sectionRef}>
      <div className="sectors-container">
        <div className="sectors-header">
          <h2 className="sectors-title">{texts[language].title}</h2>
          <p className="sectors-subtitle">{texts[language].subtitle}</p>
        </div>

        <div className="sectors-content">
          {/* Left side - Sectors list */}
          <div className="sectors-list">
            <div className="sectors-carousel">
              {visibleSectors.map((sectorIndex, displayIndex) => {
                const sector = sectors[sectorIndex];
                const position = displayIndex === 0 ? 'prev' : displayIndex === 1 ? 'current' : 'next';

                return (
                  <div
                    key={`${sector.id}-${sectorIndex}`}
                    className={`sector-item ${position}`}
                    onClick={() => handleSectorSelect(sectorIndex)}
                  >
                    <span className="sector-name">{sector.name}</span>
                    <ChevronRight className="sector-arrow" size={20} />
                  </div>
                );
              })}
            </div>

            {/* Navigation dots */}
            <div className="sectors-navigation">
              {sectors.map((_, index) => (
                <button
                  key={index}
                  className={`nav-dot ${index === currentSectorIndex ? 'active' : ''}`}
                  onClick={() => handleSectorSelect(index)}
                />
              ))}
            </div>
          </div>

          {/* Right side - Project showcase */}
          <div className="project-showcase">
            {currentProject && (
              <div
                className={`project-card ${isTransitioning ? 'transitioning' : ''}`}
                style={{
                  '--project-color': currentProject.color,
                  '--project-color-alpha': `${currentProject.color}20`
                } as React.CSSProperties}
                key={currentProject.id}
              >
                <div className="project-background">
                  <div className="project-image-overlay"></div>
                </div>

                <div className="project-content">
                  <div className="project-header">
                    <div className="project-logo">
                      <div className="project-logo-placeholder">
                        {currentProject.company.charAt(0)}
                      </div>
                    </div>
                    <div className="project-info">
                      <h3 className="project-name">{currentProject.name}</h3>
                      <p className="project-company">{currentProject.company}</p>
                    </div>
                  </div>

                  <p className="project-description">{currentProject.description}</p>

                  <button className="project-cta">
                    <span>{texts[language].viewMore}</span>
                    <ChevronRight size={16} />
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </section>
  );
};

export default SectorsSection;
