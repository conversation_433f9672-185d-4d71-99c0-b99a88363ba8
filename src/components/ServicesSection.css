/* Variables CSS */
:root {
  --primary-color: #5dcecb;
  --secondary-color: #ffffff;
  --text-color: #e0e0e0;
  --background-dark: #0a0a0a;
  --card-background: rgba(255, 255, 255, 0.05);
  --border-color: rgba(93, 206, 203, 0.2);
  --hover-glow: rgba(93, 206, 203, 0.3);
}

/* Sección principal */
.services-section {
  padding: 120px 0;
  background: radial-gradient(ellipse at center, rgba(93, 206, 203, 0.05) 0%, #000000 70%);
  position: relative;
  overflow: hidden;
}

.services-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Header de la sección */
.services-header {
  text-align: center;
  margin-bottom: 4rem;
  opacity: 0;
  transform: translateY(30px);
  animation: fadeInUp 0.8s ease-out 0.2s forwards;
}

.services-title {
  font-family: 'Sansation', sans-serif;
  font-size: 3rem;
  font-weight: 700;
  font-style: italic;
  background: linear-gradient(135deg, #ffffff 0%, #a0a0a0 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 1.5rem;
  letter-spacing: -0.02em;
  line-height: 1.2;
}

.services-subtitle {
  font-size: 1.2rem;
  color: var(--text-color);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
  opacity: 0.9;
}

/* Services Grid Layout */
.services-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  grid-template-rows: repeat(6, 1fr);
  gap: 16px;
  margin-top: 4rem;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
  min-height: 600px;
}

.services-grid .service-card {
  opacity: 0;
  transform: translateY(30px);
  animation: cardFadeIn 0.8s ease-out forwards;
}

.services-grid .item-0 {
  grid-column: 1 / span 2;
  grid-row: 1 / span 2;
  animation-delay: 0.1s;
}

.services-grid .item-1 {
  grid-column: 3 / span 4;
  grid-row: 1 / span 2;
  animation-delay: 0.2s;
}

.services-grid .item-2 {
  grid-column: 1 / span 3;
  grid-row: 3 / span 2;
  animation-delay: 0.3s;
}

.services-grid .item-3 {
  grid-column: 4 / span 3;
  grid-row: 3 / span 2;
  animation-delay: 0.4s;
}

.services-grid .item-4 {
  grid-column: 1 / span 4;
  grid-row: 5 / span 2;
  animation-delay: 0.5s;
}

.services-grid .item-5 {
  grid-column: 5 / span 2;
  grid-row: 5 / span 2;
  animation-delay: 0.6s;
}

/* Estilos de las cards */
.service-card {
  background: linear-gradient(135deg, #1a1a1a 0%, #0f0f0f 100%);
  border-radius: 20px;
  padding: 2rem;
  position: relative;
  overflow: hidden;
  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  cursor: pointer;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  width: 100%;
  height: 100%;
  min-height: 300px;
}

.service-card pixel-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  pointer-events: none;
  border-radius: 20px;
  overflow: hidden;
}

.service-card > *:not(pixel-canvas) {
  position: relative;
  z-index: 2;
}



.service-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.service-card:hover .card-overlay {
  opacity: 1;
  
}

.service-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    to top,
    rgba(0, 0, 0, 0.8) 0%,
    rgba(0, 0, 0, 0.9) 20%,
    transparent 100%
  );
  border-radius: 20px;
  z-index: 1;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.service-card:hover::after {
  opacity: 1;
}

.service-card:hover .service-video-cta { 
  opacity: 1;
}

.service-card:hover .service-description {
  opacity: 1;
  max-height: 150px;
}

/* Contenido de las cards */
.service-icon {
  font-size: 2rem;
  margin-bottom: 0.3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  opacity: 0.7;
  z-index: 3;
  position: relative;
}

.service-card:hover .service-title,
.service-card:hover .service-icon {
  opacity: 1;
}



.service-icon-svg {
  color: #ffffff;
  stroke-width: 1.5;
  transition: all 0.3s ease;
}

.service-card:hover .service-icon {
  transform: scale(1.1);
}



.service-title {
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--secondary-color);
  margin-bottom: 0.8rem;
  line-height: 1.3;
  opacity: 0.7;
  transition: opacity 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  z-index: 3;
  position: relative;
}

.service-description {
  color: var(--text-color);
  line-height: 1.5;
  margin-bottom: 0.5rem;
  flex-grow: 1;
  font-size: 0.9rem;
  opacity: 0;
  max-height: 0;
  overflow: hidden;
  transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  z-index: 1;
  position: relative;
}

/* Video CTA Styles */
.service-video-cta {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-color);
  font-weight: 500;
  font-size: 0.85rem;
  margin-top: 5px;
  opacity: 0.3;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  z-index: 4;
}

.video-icon {
  transition: all 0.3s ease;
}

.video-text {
  transition: all 0.3s ease;
}

/* Card Overlay */
.card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to top, rgba(26, 26, 26, 0.8) 0%, rgba(26, 26, 26, 0.4) 50%, transparent 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  z-index: 2;
  border-radius: 20px;
}

/* Grid responsive adjustments */
@media (max-width: 768px) {
  .services-grid {
    grid-template-columns: repeat(6, 1fr);
    grid-template-rows: repeat(6, 1fr);
    gap: 12px;
    min-height: 500px;
  }
}

/* Animaciones */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes cardFadeIn {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Responsive Design */
@media (max-width: 1200px) {
  .services-grid {
    max-width: 900px;
  }
  
  .service-card {
    min-height: 280px;
  }
}

@media (max-width: 768px) {
  .services-section {
    padding: 80px 0;
  }
  
  .services-container {
    padding: 0 1rem;
  }
  
  .services-title {
    font-size: 2.5rem;
  }
  
  .services-subtitle {
    font-size: 1.1rem;
  }
  
  .services-grid {
    max-width: 100%;
    padding: 0 1rem;
    gap: 12px;
    min-height: 500px;
  }
  
  .service-card {
    min-height: 250px;
    padding: 1.5rem;
  }
  
  .service-title {
    font-size: 1.3rem;
  }
  
  .service-description {
    font-size: 0.85rem;
  }
}

@media (max-width: 480px) {
  .services-title {
    font-size: 2rem;
  }
  
  .services-subtitle {
    font-size: 1rem;
  }
  
  .services-grid {
    gap: 8px;
    min-height: 400px;
    padding: 0 0.5rem;
  }
  
  .service-card {
    min-height: 200px;
    padding: 1.2rem;
  }
  
  .service-icon {
    font-size: 2rem;
  }
  
  .service-title {
    font-size: 1.2rem;
  }
  
  .service-description {
    font-size: 0.8rem;
  }
  
  .service-video-cta {
    font-size: 0.75rem;
  }
}